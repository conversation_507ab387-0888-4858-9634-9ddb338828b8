import json
from pinecone import Pinecone, ServerlessSpec

pc = Pinecone("pcsk_2BkTDm_5joJfVaNA5ahcWFQ7uVcpWUbrstBJj1ipAYVcmZnR8FDD1cEHKcSACvBqw7PeT7")

pc.create_index(
  name="tool-embeddings",
  vector_type="dense",
  dimension=1024,
  metric="dotproduct",
  spec=ServerlessSpec(
    cloud="aws",
    region="us-east-1"
  )
)

description = json.load(open("description_embeddings.json", "r"))

data_pc = []
for i in range(len(description)):
    data = description[i]
    if data["type"] == "component":
        id_ = f"component_{data['name']}"
    elif data["type"] == "mcp":
        id_ = f"mcp_{data['id']}_{data['name']}"
    elif data["type"] == "workflow":
        id_ = f"workflow_{data['id']}"
    value = data["embedding"]
    metadata = {k:v for k,v in data.items() if k != "embedding" and v is not None}
    data_pc.append({"id": id_, "values": value, "metadata": metadata})

index = pc.Index("tool-embeddings")
index.upsert(data_pc)

from sentence_transformers import SentenceTransformer
embedding_model = SentenceTransformer("mixedbread-ai/mxbai-embed-large-v1")

query = "Executes an AI agent with tools and memory using AutoGen."
query_embedding = embedding_model.encode(query).tolist()

index.query(vector=query_embedding, top_k=10, include_metadata=True)



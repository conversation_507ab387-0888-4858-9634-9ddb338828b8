import json
from sentence_transformers import SentenceTransformer

with open("description.json", "r") as f:
    description = json.load(f)

len(description)

embedding_model = SentenceTransformer("mixedbread-ai/mxbai-embed-large-v1")


def get_embedding(data):
    sent = f"Name : {data["name"]}\nDescription : {data["description"]}"
    if data["type"] == "mcp":
        sent += f"\nMCP Name : {data["mcp_name"]}"
    if data["category"] is not None:
        sent += f"\nCategory : {data["category"]}"
    return embedding_model.encode(sent).tolist()

for i in range(len(description)):
    description[i]["embedding"] = get_embedding(description[i])

with open("description_embeddings.json", "w") as f:
    json.dump(description, f, indent=4)


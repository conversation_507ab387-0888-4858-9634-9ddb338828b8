import os
from datetime import datetime

import requests
from pinecone import Pinecone
from sentence_transformers import SentenceTransformer

component_url = "https://app-dev.rapidinnovation.dev/api/v1/components?refresh=true"
mcp_url = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/{}"
workflow_url = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows/{}"

PINECONE_API_KEY = os.environ.get("PINECONE_API_KEY")

pc = Pinecone(PINECONE_API_KEY)
index = pc.Index("tool-embeddings")
embedding_model = SentenceTransformer("mixedbread-ai/mxbai-embed-large-v1")


def get_embedding(data):
    sent = f"Name : {data["name"]}\nDescription : {data["description"]}"
    if data["type"] == "mcp":
        sent += f"\nMCP Name : {data["mcp_name"]}"
    if data["category"] is not None:
        sent += f"\nCategory : {data["category"]}"
    
    return embedding_model.encode(sent).tolist()


def update_element(data):
    if data["type"] == "component":
        id_ = f"component_{data['name']}"
    elif data["type"] == "mcp":
        id_ = f"mcp_{data['id']}_{data['name']}"
    elif data["type"] == "workflow":
        id_ = f"workflow_{data['id']}"
    result = index.fetch(ids=[id_], include_metadata=True)
    if len(result["vectors"]) == 0:
        data["embedding"] = get_embedding(data)
        index.upsert(vectors=[(id_, data["embedding"], data)])
    else:
        if data["type"] == "component":
            return
        if "updated_at" not in result["vectors"][id_]["metadata"]:
            return
        last_updated_date = result["vectors"][id_]["metadata"]["updated_at"]
        current_updated_date = data["updated_at"]
        last_updated_date = datetime.isotformat(last_updated_date)
        current_updated_date = datetime.isotformat(current_updated_date)
        if current_updated_date > last_updated_date:
            data["embedding"] = get_embedding(data)
            metadata = {k:v for k,v in data.items() if k != "embedding" and v is not None}
            index.update(id=id_, values=data["embedding"], metadata=metadata)


def update_component():
    components = requests.get(component_url).json()
    for category in components:
        for component in components[category]:
            loop_description = {}
            loop_description["type"] = "component"
            loop_description["category"] = category
            loop_description["name"] = components[category][component]["name"]
            loop_description["description"] = components[category][component][
                "description"
            ]
            update_element(loop_description)

def update_mcp():
    mcps = requests.get("https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps?page=1&page_size=10").json()
    total_page = mcps["total_pages"]
    for page in range(1, total_page+1):
        mcps = requests.get(f"https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps?page={page}&page_size=10").json()
        for mcp in mcps["data"]:
            mcp_id = mcp["id"]
            mcp_data = requests.get(mcp_url.format(mcp_id)).json()
            mcp_id = mcp_data["mcp"]["id"]
            mcp_name = mcp_data["mcp"]["name"]
            mcp_category = mcp_data["mcp"]["category"]
            mcp_updated_at = mcp_data["mcp"]["updated_at"]
            tools = mcp_data["mcp"]["mcp_tools_config"]["tools"]
            for tool in tools:
                loop_description= {}
                loop_description["type"] = "mcp"
                loop_description["id"] = mcp_id
                loop_description["mcp_name"] = mcp_name
                loop_description["updated_at"] = mcp_updated_at
                loop_description["category"] = mcp_category
                loop_description["name"] = tool["name"]
                loop_description["description"] = tool["description"]
                update_element(loop_description)

def update_workflow():
    workflows = requests.get("https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows?page=1&page_size=10").json()
    total_page = workflows["total_pages"]
    for page in range(1, total_page+1):
        workflows = requests.get(f"https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows?page={page}&page_size=10").json()
        for workflow in workflows["data"]:
            workflow_id = workflow["id"]
            workflow_data = requests.get(workflow_url.format(workflow_id)).json()
            loop_description = {}
            loop_description["type"] = "workflow"
            loop_description["id"] = workflow_data["workflow"]["id"]
            loop_description["name"] = workflow_data["workflow"]["name"]
            loop_description["category"] = workflow_data["workflow"]["category"]
            loop_description["description"] = workflow_data["workflow"]["description"]
            loop_description["updated_at"] = workflow_data["workflow"]["updated_at"]
            update_element(loop_description)

if __name__ == "__main__":
    update_component()
    update_mcp()
    update_workflow()
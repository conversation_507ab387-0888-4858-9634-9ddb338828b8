import logging
import os
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional

import requests
from pinecone import Pinecone
from requests.adapters import HTT<PERSON>dapter
from sentence_transformers import SentenceTransformer
from urllib3.util.retry import Retry

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("update.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


@dataclass
class Config:
    """Configuration class for API endpoints and settings"""

    COMPONENT_URL: str = (
        "https://app-dev.rapidinnovation.dev/api/v1/components?refresh=true"
    )
    MCP_URL: str = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/{}"
    WORKFLOW_URL: str = (
        "https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows/{}"
    )
    MCP_LIST_URL: str = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps"
    WORKFLOW_LIST_URL: str = (
        "https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows"
    )
    PAGE_SIZE: int = 10
    REQUEST_TIMEOUT: int = 30
    MAX_RETRIES: int = 3
    BACKOFF_FACTOR: float = 0.3


config = Config()

# Environment variables
PINECONE_API_KEY = os.environ.get("PINECONE_API_KEY")
if not PINECONE_API_KEY:
    logger.error("PINECONE_API_KEY environment variable is not set")
    raise ValueError("PINECONE_API_KEY environment variable is required")

# Initialize Pinecone and embedding model
try:
    logger.info("Initializing Pinecone client...")
    pc = Pinecone(PINECONE_API_KEY)
    index = pc.Index("tool-embeddings")
    logger.info("Pinecone client initialized successfully")

    logger.info("Loading embedding model...")
    embedding_model = SentenceTransformer("mixedbread-ai/mxbai-embed-large-v1")
    logger.info("Embedding model loaded successfully")
except Exception as e:
    logger.error(f"Failed to initialize Pinecone or embedding model: {e}")
    raise


# Configure requests session with retry strategy
def create_session() -> requests.Session:
    """Create a requests session with retry strategy and timeout"""
    session = requests.Session()

    retry_strategy = Retry(
        total=config.MAX_RETRIES,
        backoff_factor=config.BACKOFF_FACTOR,
        status_forcelist=[429, 500, 502, 503, 504],
    )

    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session


session = create_session()


def get_embedding(data: Dict[str, Any]) -> List[float]:
    """Generate embedding for the given data"""
    try:
        sent = f"Name : {data['name']}\nDescription : {data['description']}"
        if data["type"] == "mcp":
            sent += f"\nMCP Name : {data['mcp_name']}"
        if data.get("category") is not None:
            sent += f"\nCategory : {data['category']}"
        else:
            sent += f"\nCategory : general"

        logger.debug(f"Generating embedding for: {data['name']}")
        embedding = embedding_model.encode(sent).tolist()
        logger.debug(f"Successfully generated embedding for: {data['name']}")
        return embedding
    except Exception as e:
        logger.error(
            f"Failed to generate embedding for {data.get('name', 'unknown')}: {e}"
        )
        raise


def safe_api_request(url: str, timeout: int = None) -> Optional[Dict[str, Any]]:
    """Make a safe API request with error handling and logging"""
    timeout = timeout or config.REQUEST_TIMEOUT
    try:
        logger.debug(f"Making API request to: {url}")
        response = session.get(url, timeout=timeout)
        response.raise_for_status()
        logger.debug(f"Successfully received response from: {url}")
        return response.json()
    except requests.exceptions.Timeout:
        logger.error(f"Request timeout for URL: {url}")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed for URL {url}: {e}")
        return None
    except ValueError as e:
        logger.error(f"Failed to parse JSON response from {url}: {e}")
        return None


def update_element(data):
    if data["type"] == "component":
        id_ = f"component_{data['name']}"
    elif data["type"] == "mcp":
        id_ = f"mcp_{data['id']}_{data['name']}"
    elif data["type"] == "workflow":
        id_ = f"workflow_{data['id']}"
    result = index.fetch(ids=[id_], include_metadata=True)
    if len(result["vectors"]) == 0:
        data["embedding"] = get_embedding(data)
        index.upsert(vectors=[(id_, data["embedding"], data)])
    else:
        if data["type"] == "component":
            return
        if "updated_at" not in result["vectors"][id_]["metadata"]:
            return
        last_updated_date = result["vectors"][id_]["metadata"]["updated_at"]
        current_updated_date = data["updated_at"]
        last_updated_date = datetime.fromisoformat(last_updated_date)
        current_updated_date = datetime.fromisoformat(current_updated_date)
        if current_updated_date > last_updated_date:
            data["embedding"] = get_embedding(data)
            metadata = {
                k: v for k, v in data.items() if k != "embedding" and v is not None
            }
            index.update(id=id_, values=data["embedding"], metadata=metadata)


def update_component():
    components = requests.get(component_url).json()
    for category in components:
        for component in components[category]:
            loop_description = {}
            loop_description["type"] = "component"
            loop_description["category"] = category
            loop_description["name"] = components[category][component]["name"]
            loop_description["description"] = components[category][component][
                "description"
            ]
            update_element(loop_description)


def update_mcp():
    mcps = requests.get(
        "https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps?page=1&page_size=10"
    ).json()
    total_page = mcps["total_pages"]
    for page in range(1, total_page + 1):
        mcps = requests.get(
            f"https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps?page={page}&page_size=10"
        ).json()
        for mcp in mcps["data"]:
            mcp_id = mcp["id"]
            mcp_data = requests.get(mcp_url.format(mcp_id)).json()
            mcp_id = mcp_data["mcp"]["id"]
            mcp_name = mcp_data["mcp"]["name"]
            mcp_category = mcp_data["mcp"]["category"]
            mcp_updated_at = mcp_data["mcp"]["updated_at"]
            tools = mcp_data["mcp"]["mcp_tools_config"]["tools"]
            for tool in tools:
                loop_description = {}
                loop_description["type"] = "mcp"
                loop_description["id"] = mcp_id
                loop_description["mcp_name"] = mcp_name
                loop_description["updated_at"] = mcp_updated_at
                loop_description["category"] = mcp_category
                loop_description["name"] = tool["name"]
                loop_description["description"] = tool["description"]
                update_element(loop_description)


def update_workflow():
    workflows = requests.get(
        "https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows?page=1&page_size=10"
    ).json()
    total_page = workflows["total_pages"]
    for page in range(1, total_page + 1):
        workflows = requests.get(
            f"https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows?page={page}&page_size=10"
        ).json()
        for workflow in workflows["data"]:
            workflow_id = workflow["id"]
            workflow_data = requests.get(workflow_url.format(workflow_id)).json()
            loop_description = {}
            loop_description["type"] = "workflow"
            loop_description["id"] = workflow_data["workflow"]["id"]
            loop_description["name"] = workflow_data["workflow"]["name"]
            loop_description["category"] = workflow_data["workflow"]["category"]
            loop_description["description"] = workflow_data["workflow"]["description"]
            loop_description["updated_at"] = workflow_data["workflow"]["updated_at"]
            update_element(loop_description)


if __name__ == "__main__":
    update_component()
    update_mcp()
    update_workflow()
